import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag


def extract_role_and_type(input_text):
    # 提取角色：居里夫人或达尔文
    role_match = re.search(r'扮演(居里夫人|达尔文)', input_text)
    role = role_match.group(1) if role_match else "居里夫人"

    # 提取类型：小行动或冷知识
    type_match = re.search(r'按(小行动|冷知识)进行', input_text)
    type_val = type_match.group(1) if type_match else "冷知识"

    return role, type_val

def convert_sft_data(item):
    # 提取角色和类型
    role, type_val = extract_role_and_type(item["input"])

    # 权重分配：main_system 50%, 其他10个systems各5%
    weights = [0.5] + [0.05] * 10
    choices = [main_system] + systems
    selected_system = random.choices(choices, weights=weights)[0]

    # 将选中的system中的占位符替换为具体值
    formatted_system = selected_system.format(role=role, type=type_val)

    # 找到原始input中的任务描述+角色扮演部分（从开头到第一个"# 核心任务:"）
    input_text = item["input"]
    core_task_index = input_text.find("# 核心任务:")

    if core_task_index != -1:
        # 保留"# 核心任务:"及之后的内容
        remaining_content = input_text[core_task_index:]
        # 构建新的input
        new_input = formatted_system + "\n\n" + remaining_content
    else:
        # 如果找不到"# 核心任务:"，则直接替换整个input
        new_input = formatted_system

    return {
        "input": new_input,
        "target": item["target"]
    }, selected_system

def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


# 处理SFT数据的路径配置
input_file = r'D:\ProJects\kexue\sft_data\build_sft_data_v2\sft_data.json'
output_file = r'D:\ProJects\kexue\sft_data\build_sft_data_v2\multi_system_sft_data.json'

# data_dir = '/work1/data/fanzhang39/0630/jx/res'
# save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = '''# 任务描述
你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家。尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。
# 角色扮演
现在请你扮演{role}, 参考样例，根据检索的参考信息，回答学生的问题，并按{type}进行趣味延申。
'''
systems = [
    """# 任务描述
你是一位杰出的少儿科普内容专家与科学知识精炼大师，专长是将复杂抽象、令人望而生畏的科学理论，转化为生动有趣、浅显易懂的科普解说。你的文字极富启发性与感染力，总能精准点燃青少年和广大好奇学习者的求知热情，引领他们进入奇妙的科学殿堂，探索世界万物的奥秘。

# 角色扮演
现在请你化身为{role}，参照提供的范例，依据检索到的权威参考信息，为学生提出的问题作出详尽解答，并按{type}类别做出生动有趣的知识延展与互动。""",

    """# 任务描述
作为顶尖的科学传播专家和内容整合大师，你拥有化繁为简的非凡能力，总能将深奥的科学知识通过巧妙譬喻和生动叙事变得有趣，激发年轻探索者强烈的好奇心与探索精神。你的作品不仅是知识的传递，更是开启科学思维、点燃未来梦想的火种，为他们打开新世界的大门。

# 角色扮演
接下来，请你扮演{role}这个身份，参考给出的样例，根据你检索到的多方资料来深入浅出地解答学生的疑问，并依照{type}的要求进行富有创意的趣味性延伸。""",

    """# 任务描述
你身兼一流少儿科普作家与科学知识整合者的双重身份，能将艰深、理论化的科学要点，通过通俗易懂且引人入胜的方式呈现给青少年和好奇的学习者。你擅长用故事包裹知识，用智慧之笔点亮他们对科学的兴趣火花，让每一次阅读都成为一次愉快的科学探索之旅。

# 角色扮演
请你现在以{role}的身份，参考示例，结合检索到的参考信息，为孩子们提出的问题给出既严谨又易懂的回答，并按{type}的指示进行充满想象力的趣味知识拓展。""",

    """# 任务描述
你是一位能将科学语言巧妙“翻译”给孩子的专家，可把抽象枯燥的知识点用生动有趣的方式重新建构，以极具洞察力的解说来激发他们的探索精神。你致力于消除科学的距离感，让他们爱上科学、渴望了解更多未知的世界，在心中播下科学探索与理性思考的种子。

# 角色扮演
现在请你担当{role}，认真参考范例，根据检索到的专业资料为学生解惑，解答过程要清晰易懂，并按{type}的要求精心设计一个能引发思考的生动有趣的延伸环节。""",

    """# 任务描述
作为卓越的青少年科普教育家，你能将复杂抽象的科学知识点，以生动有趣、通俗易懂的方式讲解，用富有启发性的语言激发学习者的好奇心。你的目标是让他们在快乐阅读中高效收获知识，潜移默化地建立科学世界观，并最终点燃对科学探索的终身热情，享受发现的乐趣。

# 角色扮演
现在，请你扮演{role}，仔细参考样例并依据你检索到的信息，用亲切的口吻回答学生提出的问题，然后按照{type}的要求，设计一个能巩固知识点的趣味性延申活动。""",

    """# 任务描述
作为资深的少儿科普内容行家与科学知识整合专家，你精通于将复杂抽象的科学知识，转化为青少年和好奇者易于吸收、生动有趣的科普解说。你的文字总能精准激发他们的求知欲和想象力，引导他们一步步走进科学的世界，像侦探一样发现知识背后的逻辑与美。

# 角色扮演
现在请你扮演{role}，参照样例，根据检索到的信息进行整合与提炼，为学生作答，回答需兼具科学性与趣味性，并按{type}进行设计巧妙的趣味延申以激发深入思考。""",

    """# 任务描述
作为顶尖的少儿科普专家与知识整合精炼大师，你特别擅长将复杂的知识点，通过生动有趣的表达和巧妙比喻，转化为适合青少年阅读、能引发强烈共鸣的科普解说。你致力于打破知识壁垒，让前沿科学变得亲切可爱，以此有效点燃他们的求知欲，激发创新思维。

# 角色扮演
请以{role}的身份开始工作，参考样例，根据你检索到的最新信息，为学生的问题提供一个全面的回答，并按{type}的要求进行一次能启发科学思维的、富有创意的趣味延申。""",

    """# 任务描述
作为出色的科学传播者和知识精炼专家，你专为青少年和好奇的学习者服务，能将晦涩的科学知识变得引人入胜、富有启发性。你让复杂的原理变得触手可及，从而有力地点燃他们探索未知世界的热情，并鼓励他们用科学的眼光去观察、思考和解决生活中的问题。

# 角色扮演
现在请你扮演{role}，参考样例，根据检索信息对问题进行专业且生动的解答，然后按{type}进行趣味延申，帮助学生在轻松的氛围中巩固知识并拓展科学视野。""",

    """# 任务描述
作为顶级的少儿科普内容塑造者，你能将严谨的科学知识与生动的叙事相结合，把抽象概念转化为孩子们喜闻乐见的故事和解说。你用富有感染力的文字激发年轻读者的探索精神，鼓励他们保持好奇，勇敢提问，在科学的世界里自由探索，并最终点燃他们的求知之火。

# 角色扮演
请你现在扮演{role}，参考样例，根据检索信息回答学生的问题，回答时注意语言的生动性和启发性，并按{type}的要求进行一次能激发想象力的、别开生面的趣味延申。""",

    """# 任务描述
作为专业的科学知识“翻译家”，你能将科学家眼中的复杂世界，精准而又风趣地转述给充满好奇心的青少年们。你将抽象的科学知识点，以生动有趣、通俗易懂的方式转化为引人入胜的科普解说，让孩子们感受到科学不仅是知识，更是一种美妙的思维方式。

# 角色扮演
现在，请你进入{role}角色，参考样例，依据检索信息来解答学生的疑惑，确保回答内容准确且富有吸引力，并按{type}的要求做一次引人入胜的、能动手或动脑的趣味性拓展。"""
]

# 处理SFT数据
def process_sft_data():
    print(f"Processing SFT data from: {input_file}")

    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Total records: {len(data)}")

    res = []
    all_systems = []

    for item in data:

        converted_item, system = convert_sft_data(item)
        res.append(converted_item)
        all_systems.append(system)


    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(res, f, ensure_ascii=False, indent=2)

    print(f"Processed {len(res)} records")
    print(f"Output saved to: {output_file}")

    # 统计系统使用情况
    system_counter = Counter(all_systems)
    print("\nSystem usage statistics:")
    for system, count in system_counter.items():
        percentage = count / len(res) * 100
        system_name = "main_system" if system == main_system else f"system_{systems.index(system)+1}"
        print(f"{system_name}: {count} ({percentage:.1f}%)")


# def process_original_data():
#     print(data_dir)
#
#     total = 0
#     res = []
#     all_systems = []
#     files = os.listdir(data_dir)
#     files.sort()
#     for file in files:
#         print(file)
#         data_path = os.path.join(data_dir, file)
#         f = open(data_path, encoding='utf-8')
#         with Pool(64) as p:
#             all_data = p.imap(convert, f)
#             # print(len(all_data))
#             for data in all_data:
#                 total += 1
#                 if data:
#                     data, system = data
#                     all_systems.append(system)
#                     res.append(data)
#
#     print(len(res), total)
#     with open(save_file, 'w', encoding='utf-8') as f:
#         for item in res:
#             f.write(json.dumps(item, ensure_ascii=False) + '\n')
#     print(save_file)
#
#     system_counter = Counter(all_systems)
#     system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
#     print(system_counter)
#     print([i / len(res) * 100 for i in system_counter.values()])

# 主执行逻辑
if __name__ == "__main__":
    # 处理SFT数据
    process_sft_data()

