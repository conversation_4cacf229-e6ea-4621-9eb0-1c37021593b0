'''
用三步法对数据
输入数据、answer进行问题诞生
'''

import json

#load数据
def load_data(input_path):
    all_data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            data = json.loads(line)
            all_data.append(data)
    return all_data

#提取这个路径的数据
def extract_data(all_data):
    for data in all_data:
        data_query = data['query']
        data_answer = data['answer']
        # print(data_query)
        # print(data_answer)
        # print(data_answer)
        break



if __name__ == '__main__':
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v2_1\资源教育-语言学习_1010_doubao-1.5-pro-32k-250115_周依凡_1754018694448_data_MultiTurnDialogue.json'
    all_data = load_data(input_path)
    extract_data(all_data)